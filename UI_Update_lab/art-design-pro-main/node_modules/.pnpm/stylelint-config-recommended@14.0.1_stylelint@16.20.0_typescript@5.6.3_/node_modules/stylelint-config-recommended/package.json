{"name": "stylelint-config-recommended", "version": "14.0.1", "description": "Recommended shareable config for Stylelint", "keywords": ["stylelint", "stylelint-config", "recommended"], "repository": "stylelint/stylelint-config-recommended", "funding": [{"type": "opencollective", "url": "https://opencollective.com/stylelint"}, {"type": "github", "url": "https://github.com/sponsors/stylelint"}], "license": "MIT", "author": "Stylelint", "main": "index.js", "files": ["index.js"], "scripts": {"format": "prettier . --write", "prepare": "husky", "lint:formatting": "prettier . --check", "lint:js": "eslint . --ignore-path .gitignore", "lint:md": "remark . --quiet --frail --ignore-path .gitignore", "lint": "npm-run-all --parallel lint:*", "release": "np --no-release-draft", "pretest": "npm run lint", "test": "node --test", "watch": "npm test --ignore-scripts -- --watch"}, "lint-staged": {"*.js": "eslint --cache --fix", "*.{js,md,yml}": "prettier --write"}, "prettier": "@stylelint/prettier-config", "eslintConfig": {"extends": ["stylelint"], "globals": {"module": true, "require": true}}, "remarkConfig": {"plugins": ["@stylelint/remark-preset"]}, "devDependencies": {"@stylelint/prettier-config": "^3.0.0", "@stylelint/remark-preset": "^5.1.1", "eslint": "^8.57.0", "eslint-config-stylelint": "^21.0.0", "husky": "^9.0.11", "lint-staged": "^15.2.7", "np": "^10.0.6", "npm-run-all": "^4.1.5", "prettier": "^3.3.2", "remark-cli": "^12.0.1", "stylelint": "^16.2.1"}, "peerDependencies": {"stylelint": "^16.1.0"}, "engines": {"node": ">=18.12.0"}}