{"version": 3, "file": "forkJoin.js", "sourceRoot": "", "sources": ["../../../../src/internal/observable/forkJoin.ts"], "names": [], "mappings": ";;;AAAA,4CAA2C;AAE3C,qEAAoE;AACpE,yCAAwC;AACxC,qCAAiD;AACjD,sEAA2E;AAC3E,6DAA4D;AAC5D,qDAAoD;AA2IpD,SAAgB,QAAQ;IAAC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,yBAAc;;IACrC,IAAM,cAAc,GAAG,wBAAiB,CAAC,IAAI,CAAC,CAAC;IACzC,IAAA,KAA0B,2CAAoB,CAAC,IAAI,CAAC,EAA5C,OAAO,UAAA,EAAE,IAAI,UAA+B,CAAC;IAC3D,IAAM,MAAM,GAAG,IAAI,uBAAU,CAAC,UAAC,UAAU;QAC/B,IAAA,MAAM,GAAK,OAAO,OAAZ,CAAa;QAC3B,IAAI,CAAC,MAAM,EAAE;YACX,UAAU,CAAC,QAAQ,EAAE,CAAC;YACtB,OAAO;SACR;QACD,IAAM,MAAM,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;QACjC,IAAI,oBAAoB,GAAG,MAAM,CAAC;QAClC,IAAI,kBAAkB,GAAG,MAAM,CAAC;gCACvB,WAAW;YAClB,IAAI,QAAQ,GAAG,KAAK,CAAC;YACrB,qBAAS,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,SAAS,CACvC,6CAAwB,CACtB,UAAU,EACV,UAAC,KAAK;gBACJ,IAAI,CAAC,QAAQ,EAAE;oBACb,QAAQ,GAAG,IAAI,CAAC;oBAChB,kBAAkB,EAAE,CAAC;iBACtB;gBACD,MAAM,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC;YAC9B,CAAC,EACD,cAAM,OAAA,oBAAoB,EAAE,EAAtB,CAAsB,EAC5B,SAAS,EACT;gBACE,IAAI,CAAC,oBAAoB,IAAI,CAAC,QAAQ,EAAE;oBACtC,IAAI,CAAC,kBAAkB,EAAE;wBACvB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,2BAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;qBAC7D;oBACD,UAAU,CAAC,QAAQ,EAAE,CAAC;iBACvB;YACH,CAAC,CACF,CACF,CAAC;;QAvBJ,KAAK,IAAI,WAAW,GAAG,CAAC,EAAE,WAAW,GAAG,MAAM,EAAE,WAAW,EAAE;oBAApD,WAAW;SAwBnB;IACH,CAAC,CAAC,CAAC;IACH,OAAO,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,mCAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AACjF,CAAC;AAvCD,4BAuCC"}