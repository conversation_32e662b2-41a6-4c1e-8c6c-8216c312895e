"use strict";

const nativeCssFunctions = new Set([
  "abs",
  "acos",
  "anchor",
  "anchor-size",
  "annotation",
  "asin",
  "atan",
  "atan2",
  "attr",
  "blur",
  "brightness",
  "calc",
  "character-variant",
  "calc-size",
  "circle",
  "clamp",
  "color",
  "color-contrast",
  "color-mix",
  "conic-gradient",
  "contrast",
  "cos",
  "counter",
  "counters",
  "cross-fade",
  "cubic-bezier",
  "device-cmyk",
  "drop-shadow",
  "element",
  "ellipse",
  "env",
  "exp",
  "fit-content",
  "format",
  "frames",
  "grayscale",
  "hsl",
  "hsla",
  "hue-rotate",
  "hwb",
  "hypot",
  "image",
  "image-set",
  "inset",
  "invert",
  "lab",
  "layer",
  "lch",
  "leader",
  "light-dark",
  "linear",
  "linear-gradient",
  "local",
  "log",
  "matrix",
  "matrix3d",
  "max",
  "min",
  "minmax",
  "mod",
  "oklab",
  "oklch",
  "opacity",
  "ornaments",
  "paint",
  "palette-mix",
  "path",
  "perspective",
  "polygon",
  "pow",
  "radial-gradient",
  "ray",
  "rect",
  "rem",
  "repeat",
  "repeating-conic-gradient",
  "repeating-linear-gradient",
  "repeating-radial-gradient",
  "rgb",
  "rgba",
  "rotate",
  "rotate3d",
  "rotateX",
  "rotatex",
  "rotateY",
  "rotatey",
  "rotateZ",
  "rotatez",
  "round",
  "saturate",
  "scale",
  "scale3d",
  "scaleX",
  "scalex",
  "scaleY",
  "scaley",
  "scaleZ",
  "scalez",
  "scroll",
  "sepia",
  "shape",
  "sign",
  "sin",
  "skew",
  "skewX",
  "skewx",
  "skewY",
  "skewy",
  "sqrt",
  "steps",
  "styleset",
  "stylistic",
  "swash",
  "symbols",
  "tan",
  "target-counter",
  "target-counters",
  "target-text",
  "translate",
  "translate3d",
  "translateX",
  "translatex",
  "translateY",
  "translatey",
  "translateZ",
  "translatez",
  "url",
  "var",
  "view",
  "xywh"
]);

/**
 * Check if a function name is a native CSS function name.
 *
 * @param {string} functionName The name to check.
 * @returns {boolean} Whether or not the given function name is a native CSS function name.
 */
module.exports = function isNativeCssFunction(functionName) {
  return nativeCssFunctions.has(functionName);
};
