<p align="center"><img src="logo.png" width="200" height="200" alt="logo" /></p>

# Known CSS properties

List of standard and browser specific CSS properties.

[![License](https://img.shields.io/github/license/known-css/known-css-properties.svg)](https://github.com/known-css/known-css-properties/blob/master/LICENSE)
[![Renovate enabled](https://img.shields.io/badge/renovate-enabled-brightgreen.svg)](https://renovateapp.com/)
[![Npm downloads](https://img.shields.io/npm/dm/known-css-properties.svg)](https://www.npmjs.com/package/known-css-properties)

## Sources

1. Standard properties (only 'REC', 'CR', 'LC', 'WD', 'FPWD', 'ED' statuses): http://www.w3.org/Style/CSS/all-properties.en.json
2. Browser supported properties from `window.getComputedStyle` / `document.body.style`

## Browser versions

### Desktop

| Name | Versions |
|---|--:|
| Chrome | 14 - 130 |
| Firefox | 6 - 131 |
| Edge | 13 - 18 |
| Safari | 1.1, 2, 6, 6.2, 7 - 9, 9.1, 10.0, 11.0, 11.1, 12.0, 12.1, 13.0, 13.1, 14, 14.1, 15.1, 15.4, 16.0, 16.5, 16.6, 17.0, 17.1, 17.3, 17.6 |
| Internet Explorer | 8 - 11 |
| Opera | 12.10, 12.14, 12.15, 12.16, 36 - 40, 45, 56, 58 |

### Mobile
| Name | Versions |
|---|--:|
| iOS Safari | 6 - 8, 8.3, 9.0, 9.3, 10.0, 10.2, 10.3, 11.0, 11.2, 11.3, 11.4, 12.0 , 12.1, 13.1, 14, 14.1, 15.1, 15.4, 15.6, 16.0, 16.4, 17.0, 17.3, 17.4, 17.5, 17.6 |
| Chrome for Android | 30, 35, 37, 44, 46, 51, 55 - 62, 64, 66 - 76, 78 - 79, 81, 83, 91, 94, 96, 100, 101, 110, 113 - 114, 117, 124 - 126, 129 |
| Firefox for Android | 47, 52 - 54, 57, 58, 62 - 64, 66, 68, 81, 85, 91, 99, 101, 106, 110, 115, 118, 120, 123, 125, 126, 131 |
| IE mobile | 11 |
| Opera Mobile | 42.7, 43, 47.1, 73.2 |
| Samsung Internet | 4.0, 6.4, 7.4, 8.2, 9.0, 14.2, 19.0, 22.0, 23.0, 25.0, 26.0 |
| UC Browser for Android | 11.2, 12.9, 12.10, 13.1, 13.4 |

## JavaScript API

```js
const properties = require('known-css-properties').all;
```

## Thanks

We use [SauceLabs](https://saucelabs.com) live testing solution for gathering most of the data.
