{"properties": ["-epub-caption-side", "-epub-hyphens", "-epub-text-combine", "-epub-text-emphasis", "-epub-text-emphasis-color", "-epub-text-emphasis-style", "-epub-text-orientation", "-epub-text-transform", "-epub-word-break", "-epub-writing-mode", "-internal-text-autosizing-status", "accelerator", "accent-color", "-wap-accesskey", "additive-symbols", "align-content", "-webkit-align-content", "align-items", "-webkit-align-items", "align-self", "-webkit-align-self", "alignment-baseline", "all", "alt", "-webkit-alt", "anchor-name", "anchor-scope", "animation", "animation-composition", "animation-delay", "-moz-animation-delay", "-ms-animation-delay", "-webkit-animation-delay", "animation-direction", "-moz-animation-direction", "-ms-animation-direction", "-webkit-animation-direction", "animation-duration", "-moz-animation-duration", "-ms-animation-duration", "-webkit-animation-duration", "animation-fill-mode", "-moz-animation-fill-mode", "-ms-animation-fill-mode", "-webkit-animation-fill-mode", "animation-iteration-count", "-moz-animation-iteration-count", "-ms-animation-iteration-count", "-webkit-animation-iteration-count", "-moz-animation", "-ms-animation", "animation-name", "-moz-animation-name", "-ms-animation-name", "-webkit-animation-name", "animation-play-state", "-moz-animation-play-state", "-ms-animation-play-state", "-webkit-animation-play-state", "animation-range", "animation-range-end", "animation-range-start", "animation-timeline", "animation-timing-function", "-moz-animation-timing-function", "-ms-animation-timing-function", "-webkit-animation-timing-function", "-webkit-animation-trigger", "-webkit-animation", "app-region", "-webkit-app-region", "appearance", "-khtml-appearance", "-moz-appearance", "-webkit-appearance", "ascent-override", "aspect-ratio", "-webkit-aspect-ratio", "audio-level", "azimuth", "backdrop-filter", "-webkit-backdrop-filter", "backface-visibility", "-moz-backface-visibility", "-ms-backface-visibility", "-webkit-backface-visibility", "background", "background-attachment", "-webkit-background-attachment", "background-blend-mode", "background-clip", "-moz-background-clip", "-webkit-background-clip", "background-color", "-webkit-background-color", "-webkit-background-composite", "background-image", "-webkit-background-image", "-moz-background-inline-policy", "background-origin", "-moz-background-origin", "-webkit-background-origin", "background-position", "-webkit-background-position", "background-position-x", "-webkit-background-position-x", "background-position-y", "-webkit-background-position-y", "background-repeat", "-webkit-background-repeat", "background-repeat-x", "background-repeat-y", "background-size", "-moz-background-size", "-webkit-background-size", "-webkit-background", "base-palette", "baseline-shift", "baseline-source", "behavior", "-khtml-binding", "-moz-binding", "block-ellipsis", "-ms-block-progression", "block-size", "block-step", "block-step-align", "block-step-insert", "block-step-round", "block-step-size", "bookmark-label", "bookmark-level", "bookmark-state", "border", "-webkit-border-after-color", "-webkit-border-after-style", "-webkit-border-after", "-webkit-border-after-width", "-webkit-border-before-color", "-webkit-border-before-style", "-webkit-border-before", "-webkit-border-before-width", "border-block", "border-block-color", "border-block-end", "border-block-end-color", "border-block-end-style", "border-block-end-width", "border-block-start", "border-block-start-color", "border-block-start-style", "border-block-start-width", "border-block-style", "border-block-width", "border-bottom", "border-bottom-color", "-moz-border-bottom-colors", "border-bottom-left-radius", "-webkit-border-bottom-left-radius", "border-bottom-right-radius", "-webkit-border-bottom-right-radius", "border-bottom-style", "border-bottom-width", "border-boundary", "border-collapse", "border-color", "-moz-border-end-color", "-webkit-border-end-color", "border-end-end-radius", "-moz-border-end", "border-end-start-radius", "-moz-border-end-style", "-webkit-border-end-style", "-webkit-border-end", "-moz-border-end-width", "-webkit-border-end-width", "-webkit-border-fit", "-khtml-border-horizontal-spacing", "-webkit-border-horizontal-spacing", "border-image", "-moz-border-image", "-o-border-image", "border-image-outset", "-webkit-border-image-outset", "border-image-repeat", "-webkit-border-image-repeat", "border-image-slice", "-webkit-border-image-slice", "border-image-source", "-webkit-border-image-source", "-webkit-border-image", "border-image-width", "-webkit-border-image-width", "border-inline", "border-inline-color", "border-inline-end", "border-inline-end-color", "border-inline-end-style", "border-inline-end-width", "border-inline-start", "border-inline-start-color", "border-inline-start-style", "border-inline-start-width", "border-inline-style", "border-inline-width", "border-left", "border-left-color", "-moz-border-left-colors", "border-left-style", "border-left-width", "border-radius", "-moz-border-radius-bottomleft", "-moz-border-radius-bottomright", "-moz-border-radius", "-moz-border-radius-topleft", "-moz-border-radius-topright", "-webkit-border-radius", "border-right", "border-right-color", "-moz-border-right-colors", "border-right-style", "border-right-width", "border-spacing", "-moz-border-start-color", "-webkit-border-start-color", "border-start-end-radius", "-moz-border-start", "border-start-start-radius", "-moz-border-start-style", "-webkit-border-start-style", "-webkit-border-start", "-moz-border-start-width", "-webkit-border-start-width", "border-style", "border-top", "border-top-color", "-moz-border-top-colors", "border-top-left-radius", "-webkit-border-top-left-radius", "border-top-right-radius", "-webkit-border-top-right-radius", "border-top-style", "border-top-width", "-khtml-border-vertical-spacing", "-webkit-border-vertical-spacing", "border-width", "bottom", "-khtml-box-align", "-moz-box-align", "-webkit-box-align", "box-decoration-break", "-webkit-box-decoration-break", "-khtml-box-direction", "-moz-box-direction", "-webkit-box-direction", "-khtml-box-flex-group", "-khtml-box-flex-group-transition", "-webkit-box-flex-group", "-khtml-box-flex", "-moz-box-flex", "-webkit-box-flex", "-khtml-box-lines", "-webkit-box-lines", "-khtml-box-ordinal-group", "-moz-box-ordinal-group", "-webkit-box-ordinal-group", "-khtml-box-orient", "-moz-box-orient", "-webkit-box-orient", "-khtml-box-pack", "-moz-box-pack", "-webkit-box-pack", "-webkit-box-reflect", "box-shadow", "-moz-box-shadow", "-webkit-box-shadow", "box-sizing", "-moz-box-sizing", "-webkit-box-sizing", "box-snap", "break-after", "break-before", "break-inside", "buffered-rendering", "caption-side", "caret", "caret-animation", "caret-color", "caret-shape", "chains", "clear", "clip", "clip-path", "-webkit-clip-path", "clip-rule", "color", "color-adjust", "-webkit-color-correction", "-apple-color-filter", "color-interpolation", "color-interpolation-filters", "color-profile", "color-rendering", "color-scheme", "-webkit-column-axis", "-webkit-column-break-after", "-webkit-column-break-before", "-webkit-column-break-inside", "column-count", "-moz-column-count", "-webkit-column-count", "column-fill", "-moz-column-fill", "-webkit-column-fill", "column-gap", "-moz-column-gap", "-webkit-column-gap", "column-progression", "-webkit-column-progression", "column-rule", "column-rule-color", "-moz-column-rule-color", "-webkit-column-rule-color", "-moz-column-rule", "column-rule-style", "-moz-column-rule-style", "-webkit-column-rule-style", "-webkit-column-rule", "column-rule-width", "-moz-column-rule-width", "-webkit-column-rule-width", "column-span", "-moz-column-span", "-webkit-column-span", "column-width", "-moz-column-width", "-webkit-column-width", "columns", "-moz-columns", "-webkit-columns", "-webkit-composition-fill-color", "-webkit-composition-frame-color", "contain", "contain-intrinsic-block-size", "contain-intrinsic-height", "contain-intrinsic-inline-size", "contain-intrinsic-size", "contain-intrinsic-width", "container", "container-name", "container-type", "content", "content-visibility", "-ms-content-zoom-chaining", "-ms-content-zoom-limit-max", "-ms-content-zoom-limit-min", "-ms-content-zoom-limit", "-ms-content-zoom-snap", "-ms-content-zoom-snap-points", "-ms-content-zoom-snap-type", "-ms-content-zooming", "continue", "counter-increment", "counter-reset", "counter-set", "cue", "cue-after", "cue-before", "cursor", "-webkit-cursor-visibility", "cx", "cy", "d", "-apple-dashboard-region", "-khtml-dashboard-region", "-webkit-dashboard-region", "descent-override", "direction", "display", "display-align", "dominant-baseline", "elevation", "empty-cells", "enable-background", "epub-caption-side", "epub-hyphens", "epub-text-combine", "epub-text-emphasis", "epub-text-emphasis-color", "epub-text-emphasis-style", "epub-text-orientation", "epub-text-transform", "epub-word-break", "epub-writing-mode", "fallback", "field-sizing", "fill", "fill-break", "fill-color", "fill-image", "fill-opacity", "fill-origin", "fill-position", "fill-repeat", "fill-rule", "fill-size", "filter", "-ms-filter", "-webkit-filter", "flex", "-ms-flex-align", "-webkit-flex-align", "flex-basis", "-webkit-flex-basis", "flex-direction", "-ms-flex-direction", "-webkit-flex-direction", "flex-flow", "-ms-flex-flow", "-webkit-flex-flow", "flex-grow", "-webkit-flex-grow", "-ms-flex-item-align", "-webkit-flex-item-align", "-ms-flex-line-pack", "-webkit-flex-line-pack", "-ms-flex", "-ms-flex-negative", "-ms-flex-order", "-webkit-flex-order", "-ms-flex-pack", "-webkit-flex-pack", "-ms-flex-positive", "-ms-flex-preferred-size", "flex-shrink", "-webkit-flex-shrink", "-webkit-flex", "flex-wrap", "-ms-flex-wrap", "-webkit-flex-wrap", "float", "float-defer", "-moz-float-edge", "float-offset", "float-reference", "flood-color", "flood-opacity", "flow", "flow-from", "-ms-flow-from", "-webkit-flow-from", "flow-into", "-ms-flow-into", "-webkit-flow-into", "-khtml-flow-mode", "-konq-flow-mode", "font", "font-display", "font-family", "font-feature-settings", "-moz-font-feature-settings", "-ms-font-feature-settings", "-webkit-font-feature-settings", "font-kerning", "-webkit-font-kerning", "font-language-override", "-moz-font-language-override", "font-optical-sizing", "font-palette", "font-size", "font-size-adjust", "-khtml-font-size-delta", "-webkit-font-size-delta", "-webkit-font-smoothing", "font-stretch", "font-style", "font-synthesis", "font-synthesis-position", "font-synthesis-small-caps", "font-synthesis-style", "font-synthesis-weight", "font-variant", "font-variant-alternates", "font-variant-caps", "font-variant-east-asian", "font-variant-emoji", "font-variant-ligatures", "-webkit-font-variant-ligatures", "font-variant-numeric", "font-variant-position", "font-variation-settings", "font-weight", "font-width", "footnote-display", "footnote-policy", "-moz-force-broken-image-icon", "forced-color-adjust", "gap", "glyph-orientation-horizontal", "glyph-orientation-vertical", "grid", "-webkit-grid-after", "grid-area", "grid-auto-columns", "-webkit-grid-auto-columns", "grid-auto-flow", "-webkit-grid-auto-flow", "grid-auto-rows", "-webkit-grid-auto-rows", "-webkit-grid-before", "grid-column", "-ms-grid-column-align", "grid-column-end", "grid-column-gap", "-ms-grid-column", "-ms-grid-column-span", "grid-column-start", "-webkit-grid-column", "-ms-grid-columns", "-webkit-grid-columns", "-webkit-grid-end", "grid-gap", "grid-row", "-ms-grid-row-align", "grid-row-end", "grid-row-gap", "-ms-grid-row", "-ms-grid-row-span", "grid-row-start", "-webkit-grid-row", "-ms-grid-rows", "-webkit-grid-rows", "-webkit-grid-start", "grid-template", "grid-template-areas", "grid-template-columns", "grid-template-rows", "hanging-punctuation", "height", "-ms-high-contrast-adjust", "-webkit-highlight", "-khtml-horizontal-border-spacing", "hyphenate-character", "-webkit-hyphenate-character", "-webkit-hyphenate-limit-after", "-webkit-hyphenate-limit-before", "hyphenate-limit-chars", "-ms-hyphenate-limit-chars", "hyphenate-limit-last", "hyphenate-limit-lines", "-ms-hyphenate-limit-lines", "-webkit-hyphenate-limit-lines", "hyphenate-limit-zone", "-ms-hyphenate-limit-zone", "hyphens", "-moz-hyphens", "-ms-hyphens", "-webkit-hyphens", "image-orientation", "-moz-image-region", "image-rendering", "image-resolution", "-ms-ime-align", "ime-mode", "inherits", "initial-letter", "initial-letter-align", "-webkit-initial-letter", "initial-letter-wrap", "initial-value", "inline-size", "inline-sizing", "input-format", "-wap-input-format", "-wap-input-required", "input-security", "inset", "inset-area", "inset-block", "inset-block-end", "inset-block-start", "inset-inline", "inset-inline-end", "inset-inline-start", "interpolate-size", "-ms-interpolation-mode", "isolation", "-konq-js-clip", "justify-content", "-webkit-justify-content", "justify-items", "-webkit-justify-items", "justify-self", "-webkit-justify-self", "kerning", "layout-flow", "layout-grid", "layout-grid-char", "layout-grid-line", "layout-grid-mode", "layout-grid-type", "left", "letter-spacing", "lighting-color", "-webkit-line-align", "-webkit-line-box-contain", "line-break", "-khtml-line-break", "-webkit-line-break", "line-clamp", "-apple-line-clamp", "-khtml-line-clamp", "-webkit-line-clamp", "line-fit-edge", "line-gap-override", "line-grid", "-webkit-line-grid-snap", "-webkit-line-grid", "line-height", "line-height-step", "line-increment", "line-padding", "line-snap", "-webkit-line-snap", "-o-link", "-o-link-source", "list-style", "list-style-image", "list-style-position", "list-style-type", "-webkit-locale", "-webkit-logical-height", "-webkit-logical-width", "margin", "-webkit-margin-after-collapse", "-webkit-margin-after", "-webkit-margin-before-collapse", "-webkit-margin-before", "margin-block", "margin-block-end", "margin-block-start", "margin-bottom", "-khtml-margin-bottom-collapse", "-webkit-margin-bottom-collapse", "margin-break", "-khtml-margin-collapse", "-webkit-margin-collapse", "-moz-margin-end", "-webkit-margin-end", "margin-inline", "margin-inline-end", "margin-inline-start", "margin-left", "margin-right", "-khtml-margin-start", "-moz-margin-start", "-webkit-margin-start", "margin-top", "-khtml-margin-top-collapse", "-webkit-margin-top-collapse", "margin-trim", "marker", "marker-end", "marker-knockout-left", "marker-knockout-right", "marker-mid", "marker-offset", "marker-pattern", "marker-segment", "marker-side", "marker-start", "marks", "-wap-marquee-dir", "-khtml-marquee-direction", "-webkit-marquee-direction", "-khtml-marquee-increment", "-webkit-marquee-increment", "-khtml-marquee", "-wap-marquee-loop", "-khtml-marquee-repetition", "-webkit-marquee-repetition", "-khtml-marquee-speed", "-wap-marquee-speed", "-webkit-marquee-speed", "-khtml-marquee-style", "-wap-marquee-style", "-webkit-marquee-style", "-webkit-marquee", "mask", "-webkit-mask-attachment", "mask-border", "mask-border-mode", "mask-border-outset", "mask-border-repeat", "mask-border-slice", "mask-border-source", "mask-border-width", "-webkit-mask-box-image-outset", "-webkit-mask-box-image-repeat", "-webkit-mask-box-image-slice", "-webkit-mask-box-image-source", "-webkit-mask-box-image", "-webkit-mask-box-image-width", "mask-clip", "-webkit-mask-clip", "mask-composite", "-webkit-mask-composite", "mask-image", "-webkit-mask-image", "mask-mode", "mask-origin", "-webkit-mask-origin", "mask-position", "-webkit-mask-position", "mask-position-x", "-webkit-mask-position-x", "mask-position-y", "-webkit-mask-position-y", "mask-repeat", "-webkit-mask-repeat", "-webkit-mask-repeat-x", "-webkit-mask-repeat-y", "mask-size", "-webkit-mask-size", "mask-source-type", "-webkit-mask-source-type", "mask-type", "-webkit-mask", "masonry", "masonry-auto-tracks", "masonry-direction", "masonry-fill", "masonry-flow", "masonry-slack", "masonry-template-areas", "masonry-template-tracks", "-khtml-match-nearest-mail-blockquote-color", "-webkit-match-nearest-mail-blockquote-color", "math-depth", "math-shift", "math-style", "max-block-size", "max-height", "max-inline-size", "max-lines", "-webkit-max-logical-height", "-webkit-max-logical-width", "max-width", "max-zoom", "min-block-size", "min-height", "min-inline-size", "min-intrinsic-sizing", "-webkit-min-logical-height", "-webkit-min-logical-width", "min-width", "min-zoom", "mix-blend-mode", "motion", "motion-offset", "motion-path", "motion-rotation", "nav-down", "nav-index", "nav-left", "nav-right", "nav-up", "navigation", "-khtml-nbsp-mode", "-webkit-nbsp-mode", "negative", "object-fit", "-o-object-fit", "object-position", "-o-object-position", "object-view-box", "offset", "offset-anchor", "offset-block-end", "offset-block-start", "offset-distance", "offset-inline-end", "offset-inline-start", "offset-path", "offset-position", "offset-rotate", "offset-rotation", "opacity", "-khtml-opacity", "-moz-opacity", "-webkit-opacity", "order", "-webkit-order", "-moz-orient", "orientation", "orphans", "-moz-osx-font-smoothing", "outline", "outline-color", "-moz-outline-color", "-moz-outline", "outline-offset", "-moz-outline-offset", "-moz-outline-radius-bottomleft", "-moz-outline-radius-bottomright", "-moz-outline-radius", "-moz-outline-radius-topleft", "-moz-outline-radius-topright", "outline-style", "-moz-outline-style", "outline-width", "-moz-outline-width", "overflow", "overflow-anchor", "overflow-block", "overflow-clip-margin", "overflow-clip-margin-block", "overflow-clip-margin-block-end", "overflow-clip-margin-block-start", "overflow-clip-margin-bottom", "overflow-clip-margin-inline", "overflow-clip-margin-inline-end", "overflow-clip-margin-inline-start", "overflow-clip-margin-left", "overflow-clip-margin-right", "overflow-clip-margin-top", "overflow-inline", "-webkit-overflow-scrolling", "-ms-overflow-style", "overflow-wrap", "overflow-x", "overflow-y", "overlay", "override-colors", "overscroll-behavior", "overscroll-behavior-block", "overscroll-behavior-inline", "overscroll-behavior-x", "overscroll-behavior-y", "pad", "padding", "-webkit-padding-after", "-webkit-padding-before", "padding-block", "padding-block-end", "padding-block-start", "padding-bottom", "-moz-padding-end", "-webkit-padding-end", "padding-inline", "padding-inline-end", "padding-inline-start", "padding-left", "padding-right", "-khtml-padding-start", "-moz-padding-start", "-webkit-padding-start", "padding-top", "page", "page-break-after", "page-break-before", "page-break-inside", "page-orientation", "paint-order", "pause", "pause-after", "pause-before", "-apple-pay-button-style", "-apple-pay-button-type", "pen-action", "perspective", "-moz-perspective", "-ms-perspective", "perspective-origin", "-moz-perspective-origin", "-ms-perspective-origin", "-webkit-perspective-origin", "perspective-origin-x", "-webkit-perspective-origin-x", "perspective-origin-y", "-webkit-perspective-origin-y", "-webkit-perspective", "pitch", "pitch-range", "place-content", "place-items", "place-self", "play-during", "pointer-events", "position", "position-anchor", "position-area", "position-try", "position-try-fallbacks", "position-try-options", "position-try-order", "position-visibility", "prefix", "print-color-adjust", "-webkit-print-color-adjust", "property-name", "quotes", "r", "range", "-webkit-region-break-after", "-webkit-region-break-before", "-webkit-region-break-inside", "region-fragment", "-webkit-region-fragment", "-webkit-region-overflow", "resize", "rest", "rest-after", "rest-before", "richness", "right", "rotate", "row-gap", "-khtml-rtl-ordering", "-webkit-rtl-ordering", "ruby-align", "ruby-merge", "ruby-overhang", "ruby-position", "-webkit-ruby-position", "running", "rx", "ry", "scale", "scroll-behavior", "-ms-scroll-chaining", "-ms-scroll-limit", "-ms-scroll-limit-x-max", "-ms-scroll-limit-x-min", "-ms-scroll-limit-y-max", "-ms-scroll-limit-y-min", "scroll-margin", "scroll-margin-block", "scroll-margin-block-end", "scroll-margin-block-start", "scroll-margin-bottom", "scroll-margin-inline", "scroll-margin-inline-end", "scroll-margin-inline-start", "scroll-margin-left", "scroll-margin-right", "scroll-margin-top", "scroll-padding", "scroll-padding-block", "scroll-padding-block-end", "scroll-padding-block-start", "scroll-padding-bottom", "scroll-padding-inline", "scroll-padding-inline-end", "scroll-padding-inline-start", "scroll-padding-left", "scroll-padding-right", "scroll-padding-top", "-ms-scroll-rails", "scroll-snap-align", "scroll-snap-coordinate", "-webkit-scroll-snap-coordinate", "scroll-snap-destination", "-webkit-scroll-snap-destination", "scroll-snap-margin", "scroll-snap-margin-bottom", "scroll-snap-margin-left", "scroll-snap-margin-right", "scroll-snap-margin-top", "scroll-snap-points-x", "-ms-scroll-snap-points-x", "-webkit-scroll-snap-points-x", "scroll-snap-points-y", "-ms-scroll-snap-points-y", "-webkit-scroll-snap-points-y", "scroll-snap-stop", "scroll-snap-type", "-ms-scroll-snap-type", "-webkit-scroll-snap-type", "scroll-snap-type-x", "scroll-snap-type-y", "-ms-scroll-snap-x", "-ms-scroll-snap-y", "scroll-start-target", "scroll-timeline", "scroll-timeline-axis", "scroll-timeline-name", "-ms-scroll-translation", "scrollbar-arrow-color", "scrollbar-base-color", "scrollbar-color", "scrollbar-dark-shadow-color", "scrollbar-darkshadow-color", "scrollbar-face-color", "scrollbar-gutter", "scrollbar-highlight-color", "scrollbar-shadow-color", "scrollbar-track-color", "scrollbar-width", "scrollbar3d-light-color", "scrollbar3dlight-color", "shape-image-threshold", "-webkit-shape-image-threshold", "shape-inside", "-webkit-shape-inside", "shape-margin", "-webkit-shape-margin", "shape-outside", "-webkit-shape-outside", "-webkit-shape-padding", "shape-rendering", "size", "size-adjust", "snap-height", "solid-color", "solid-opacity", "spatial-navigation-action", "spatial-navigation-contain", "spatial-navigation-function", "speak", "speak-as", "speak-header", "speak-numeral", "speak-punctuation", "speech-rate", "src", "-moz-stack-sizing", "stop-color", "stop-opacity", "stress", "string-set", "stroke", "stroke-align", "stroke-alignment", "stroke-break", "stroke-color", "stroke-dash-corner", "stroke-dash-justify", "stroke-dash<PERSON>just", "stroke-dasharray", "stroke-dashcorner", "stroke-dashoffset", "stroke-image", "stroke-linecap", "stroke-linejoin", "stroke-miterlimit", "stroke-opacity", "stroke-origin", "stroke-position", "stroke-repeat", "stroke-size", "stroke-width", "suffix", "supported-color-schemes", "-webkit-svg-shadow", "symbols", "syntax", "system", "tab-size", "-moz-tab-size", "-o-tab-size", "-o-table-baseline", "table-layout", "-webkit-tap-highlight-color", "text-align", "text-align-all", "text-align-last", "-moz-text-align-last", "text-anchor", "text-autospace", "-moz-text-blink", "text-box", "text-box-edge", "text-box-trim", "-ms-text-combine-horizontal", "text-combine-upright", "-webkit-text-combine", "text-decoration", "text-decoration-blink", "text-decoration-color", "-moz-text-decoration-color", "-webkit-text-decoration-color", "text-decoration-line", "-moz-text-decoration-line", "text-decoration-line-through", "-webkit-text-decoration-line", "text-decoration-none", "text-decoration-overline", "text-decoration-skip", "text-decoration-skip-box", "text-decoration-skip-ink", "text-decoration-skip-inset", "text-decoration-skip-self", "text-decoration-skip-spaces", "-webkit-text-decoration-skip", "text-decoration-style", "-moz-text-decoration-style", "-webkit-text-decoration-style", "text-decoration-thickness", "text-decoration-trim", "text-decoration-underline", "-webkit-text-decoration", "-khtml-text-decorations-in-effect", "-webkit-text-decorations-in-effect", "text-emphasis", "text-emphasis-color", "-webkit-text-emphasis-color", "text-emphasis-position", "-webkit-text-emphasis-position", "text-emphasis-skip", "text-emphasis-style", "-webkit-text-emphasis-style", "-webkit-text-emphasis", "-webkit-text-fill-color", "text-group-align", "text-indent", "text-justify", "text-justify-trim", "text-ka<PERSON><PERSON>", "text-kashida-space", "text-line-through", "text-line-through-color", "text-line-through-mode", "text-line-through-style", "text-line-through-width", "text-orientation", "-webkit-text-orientation", "text-overflow", "text-overline", "text-overline-color", "text-overline-mode", "text-overline-style", "text-overline-width", "text-rendering", "-webkit-text-security", "text-shadow", "text-size-adjust", "-apple-text-size-adjust", "-khtml-text-size-adjust", "-moz-text-size-adjust", "-ms-text-size-adjust", "-webkit-text-size-adjust", "text-spacing", "text-spacing-trim", "-webkit-text-stroke-color", "-webkit-text-stroke", "-webkit-text-stroke-width", "text-transform", "text-underline", "text-underline-color", "text-underline-mode", "text-underline-offset", "text-underline-position", "-webkit-text-underline-position", "text-underline-style", "text-underline-width", "text-wrap", "text-wrap-mode", "text-wrap-style", "-webkit-text-zoom", "timeline-scope", "top", "touch-action", "touch-action-delay", "-ms-touch-action", "-webkit-touch-callout", "-ms-touch-select", "-apple-trailing-word", "transform", "transform-box", "-moz-transform", "-ms-transform", "-o-transform", "transform-origin", "-moz-transform-origin", "-ms-transform-origin", "-o-transform-origin", "-webkit-transform-origin", "transform-origin-x", "-webkit-transform-origin-x", "transform-origin-y", "-webkit-transform-origin-y", "transform-origin-z", "-webkit-transform-origin-z", "transform-style", "-moz-transform-style", "-ms-transform-style", "-webkit-transform-style", "-webkit-transform", "transition", "transition-behavior", "transition-delay", "-moz-transition-delay", "-ms-transition-delay", "-o-transition-delay", "-webkit-transition-delay", "transition-duration", "-moz-transition-duration", "-ms-transition-duration", "-o-transition-duration", "-webkit-transition-duration", "-moz-transition", "-ms-transition", "-o-transition", "transition-property", "-moz-transition-property", "-ms-transition-property", "-o-transition-property", "-webkit-transition-property", "transition-timing-function", "-moz-transition-timing-function", "-ms-transition-timing-function", "-o-transition-timing-function", "-webkit-transition-timing-function", "-webkit-transition", "translate", "types", "uc-alt-skin", "uc-skin", "unicode-bidi", "unicode-range", "-khtml-user-drag", "-webkit-user-drag", "-moz-user-focus", "-moz-user-input", "-khtml-user-modify", "-moz-user-modify", "-webkit-user-modify", "user-select", "-khtml-user-select", "-moz-user-select", "-ms-user-select", "-webkit-user-select", "user-zoom", "vector-effect", "vertical-align", "-khtml-vertical-border-spacing", "view-timeline", "view-timeline-axis", "view-timeline-inset", "view-timeline-name", "view-transition-class", "view-transition-group", "view-transition-name", "viewport-fill", "viewport-fill-opacity", "viewport-fit", "visibility", "voice-balance", "voice-duration", "voice-family", "voice-pitch", "voice-range", "voice-rate", "voice-stress", "voice-volume", "volume", "white-space", "white-space-collapse", "white-space-trim", "-webkit-widget-region", "widows", "width", "will-change", "-moz-window-dragging", "-moz-window-shadow", "word-break", "word-space-transform", "word-spacing", "word-wrap", "wrap-after", "wrap-before", "wrap-flow", "-ms-wrap-flow", "-webkit-wrap-flow", "wrap-inside", "-ms-wrap-margin", "-webkit-wrap-margin", "-webkit-wrap-padding", "-webkit-wrap-shape-inside", "-webkit-wrap-shape-outside", "wrap-through", "-ms-wrap-through", "-webkit-wrap-through", "-webkit-wrap", "writing-mode", "-webkit-writing-mode", "x", "y", "z-index", "zoom"]}