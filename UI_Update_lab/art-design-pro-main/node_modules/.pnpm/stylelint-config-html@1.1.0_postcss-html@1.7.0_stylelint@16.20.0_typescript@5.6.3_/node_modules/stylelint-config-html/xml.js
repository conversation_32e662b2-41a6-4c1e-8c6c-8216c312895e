"use strict";

const extensions = [
  // https://github.com/Microsoft/vscode/blob/master/extensions/xml/package.json
  ".xml",
  ".xsd",
  ".ascx",
  ".atom",
  ".axml",
  ".axaml",
  ".bpmn",
  ".cpt",
  ".csl",
  ".csproj",
  ".csproj.user",
  ".dita",
  ".ditamap",
  ".dtd",
  ".ent",
  ".mod",
  ".dtml",
  ".fsproj",
  ".fxml",
  ".iml",
  ".isml",
  ".jmx",
  ".launch",
  ".menu",
  ".mxml",
  ".nuspec",
  ".opml",
  ".owl",
  ".proj",
  ".props",
  ".pt",
  ".publishsettings",
  ".pubxml",
  ".pubxml.user",
  ".rbxlx",
  ".rbxmx",
  ".rdf",
  ".rng",
  ".rss",
  ".shproj",
  ".storyboard",
  ".svg",
  ".targets",
  ".tld",
  ".tmx",
  ".vbproj",
  ".vbproj.user",
  ".vcxproj",
  ".vcxproj.filters",
  ".wsdl",
  ".wxi",
  ".wxl",
  ".wxs",
  ".xaml",
  ".xbl",
  ".xib",
  ".xlf",
  ".xliff",
  ".xpdl",
  ".xul",
  ".xoml",

  // xsl
  ".xsl",
  ".xslt",
];
module.exports = {
  overrides: [
    {
      files: extensions.flatMap((ext) => [`*${ext}`, `**/*${ext}`]),
      customSyntax: "postcss-html",
    },
  ],
};
