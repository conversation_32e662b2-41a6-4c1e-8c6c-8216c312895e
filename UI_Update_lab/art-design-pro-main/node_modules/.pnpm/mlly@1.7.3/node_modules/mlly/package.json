{"name": "mlly", "version": "1.7.3", "description": "Missing ECMAScript module utils for Node.js", "repository": "unjs/mlly", "license": "MIT", "sideEffects": false, "type": "module", "exports": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint src test && prettier -c src test", "lint:fix": "eslint src test --fix && prettier -w src test", "release": "pnpm test && pnpm build && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run", "test:types": "tsc --noEmit"}, "dependencies": {"acorn": "^8.14.0", "pathe": "^1.1.2", "pkg-types": "^1.2.1", "ufo": "^1.5.4"}, "devDependencies": {"@types/node": "^22.9.0", "@vitest/coverage-v8": "^2.1.4", "changelogen": "^0.5.7", "eslint": "^9.14.0", "eslint-config-unjs": "^0.4.1", "import-meta-resolve": "^4.1.0", "jiti": "^2.4.0", "prettier": "^3.3.3", "std-env": "^3.8.0", "typescript": "^5.6.3", "unbuild": "^2.0.0", "vitest": "^2.1.4"}, "packageManager": "pnpm@9.12.3"}