/**
 * SSR Window 3.0.0
 * Better handling for window object in SSR environment
 * https://github.com/nolimits4web/ssr-window
 *
 * Copyright 2020, <PERSON>
 *
 * Licensed under MIT
 *
 * Released on: November 9, 2020
 */
!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e=e||self).ssrWindow={})}(this,(function(e){"use strict";function n(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function t(e,o){void 0===e&&(e={}),void 0===o&&(o={}),Object.keys(o).forEach((function(u){void 0===e[u]?e[u]=o[u]:n(o[u])&&n(e[u])&&Object.keys(o[u]).length>0&&t(e[u],o[u])}))}var o={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};var u={document:o,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};e.extend=t,e.getDocument=function(){var e="undefined"!=typeof document?document:{};return t(e,o),e},e.getWindow=function(){var e="undefined"!=typeof window?window:{};return t(e,u),e},e.ssrDocument=o,e.ssrWindow=u,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=ssr-window.umd.min.js.map