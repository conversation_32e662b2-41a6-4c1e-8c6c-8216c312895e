{"name": "stylelint-config-recess-order", "version": "4.6.0", "description": "Recess-based property sort order for Stylelint.", "keywords": ["bootstrap", "properties-order", "property order", "recess", "stylelint", "stylelint-config", "stylelint-order"], "homepage": "https://github.com/stormwarning/stylelint-config-recess-order", "bugs": "https://github.com/stormwarning/stylelint-config-recess-order/issues", "repository": "stormwarning/stylelint-config-recess-order", "license": "ISC", "author": "<PERSON> (https://tidaltheory.io)", "main": "index.js", "files": ["index.js", "groups.js"], "lint-staged": {"*.js": ["eslint --fix", "prettier --write"], "package.json": "prettier --write"}, "prettier": {"semi": false, "singleQuote": true, "trailingComma": "all"}, "ava": {"require": ["esm"]}, "dependencies": {"stylelint-order": "6.x"}, "devDependencies": {"@changesets/cli": "2.27.1", "@zazen/changesets-changelog": "2.0.3", "@zazen/eslint-config": "6.7.0", "ava": "5.2.0", "eslint": "8.56.0", "esm": "3.2.25", "husky": "9.0.10", "lint-staged": "15.2.2", "prettier": "3.2.5", "prettier-plugin-packagejson": "2.4.10", "stylelint": "15.1.0", "typescript": "4.9.5"}, "peerDependencies": {"stylelint": ">=15"}, "scripts": {"changeset": "changeset add", "lint": "eslint '**/*.js'", "release": "changeset publish", "test": "ava"}, "readme": "# Recess\\* Property Order [<img src=\"https://s3.amazonaws.com/media-p.slid.es/uploads/467124/images/2872758/stylelint-icon-black.svg\" alt=\"StyleLint\" width=\"90\" height=\"90\" align=\"right\">][stylelint]\n\n[![npm version][npm-img]][npm-url]\n[![npm downloads][npm-dls]][npm-url]\n[![github issues][issues-img]][issues-url]\n\nA [Stylelint][] config that sorts CSS properties the way [Recess][] did and\nBootstrap [did][]/[does][].\n\n\\*With some modifications & additions for modern properties.\n\n## Usage\n\n1.  Add [stylelint][] and this package to your project:\n    ```sh\n    npm install --save-dev stylelint stylelint-config-recess-order\n    ```\n2.  Configure your stylelint configuration file to extend this package:\n    ```js\n    module.exports = {\n    \textends: ['stylelint-config-recess-order'],\n    \trules: {\n    \t\t// Add overrides and additional rules here\n    \t},\n    }\n    ```\n\n## Advanced\n\nThe default setup applies only the `'order/properties-order'` rule with the various property groups. If you need to configure other options for this rule,\nthe groups can be imported separately and the rule configured to your needs.\n\n```js\nconst propertyGroups = require('stylelint-config-recess-order/groups')\n\nmodule.exports = {\n\textends: [], // Do not extend the config here.\n\trules: {\n\t\t// Configure the rule manually.\n\t\t'order/properties-order': propertyGroups.map((group) => ({\n\t\t\t...group,\n\t\t\temptyLineBefore: 'always',\n\t\t\tnoEmptyLineBetween: true,\n\t\t})),\n\t},\n}\n```\n\n## References\n\n[@mdo on CSS Property Order][mdo-order]\n\n[npm-url]: https://www.npmjs.com/package/stylelint-config-recess-order\n[npm-img]: https://img.shields.io/npm/v/stylelint-config-recess-order.svg?style=flat-square\n[npm-dls]: https://img.shields.io/npm/dt/stylelint-config-recess-order.svg?style=flat-square\n[issues-url]: https://github.com/stormwarning/stylelint-config-recess-order/issues\n[issues-img]: https://img.shields.io/github/issues/stormwarning/stylelint-config-recess-order.svg?style=flat-square\n[stylelint]: https://github.com/stylelint/stylelint\n[recess]: https://github.com/twitter/recess/blob/29bccc870b7b4ccaa0a138e504caf608a6606b59/lib/lint/strict-property-order.js\n[did]: https://github.com/twbs/bootstrap/blob/f58997a0dae54dc98d11892afef9acb85bdc6a1e/.scss-lint.yml#L136\n[does]: https://github.com/twbs/stylelint-config-twbs-bootstrap/blob/ad67be6e4ceb48809fa1dce13b7892f9d2018995/css/index.js#L38\n[mdo-order]: http://markdotto.com/2011/11/29/css-property-order/\n"}