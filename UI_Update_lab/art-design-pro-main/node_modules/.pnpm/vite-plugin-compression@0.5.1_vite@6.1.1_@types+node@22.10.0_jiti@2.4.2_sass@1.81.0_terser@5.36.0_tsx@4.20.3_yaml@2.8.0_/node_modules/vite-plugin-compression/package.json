{"name": "vite-plugin-compression", "version": "0.5.1", "description": "Use gzip or brotli to compress resources.", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.mjs", "types": "./dist/index.d.ts"}}, "license": "MIT", "author": "Vben", "files": ["dist"], "keywords": ["vite", "vite-plugin", "imagemin", "vben"], "repository": {"type": "git", "url": "https://github.com/anncwb/vite-plugin-compression", "directory": "packages/core"}, "bugs": {"url": "https://github.com/anncwb/vite-plugin-compression/issues"}, "homepage": "https://github.com/anncwb/vite-plugin-compression/tree/master/#readme", "dependencies": {"chalk": "^4.1.2", "debug": "^4.3.3", "fs-extra": "^10.0.0"}, "peerDependencies": {"vite": ">=2.0.0"}, "devDependencies": {"@types/chalk": "^2.2.0", "@types/debug": "^4.1.7", "@types/fs-extra": "^9.0.13", "@types/node": "^17.0.13"}, "scripts": {"dev": "pnpm unbuild --stub", "build": "pnpm unbuild"}}