# Welcome to MDN data

> **Note**
> We are in the process of deprecating the `mdn/data` package in favor of [`w3c/webref`](https://github.com/w3c/webref).
> If you depend on this project, let us know in our community [GitHub discussions](https://github.com/mdn/mdn-community/discussions/categories/platform).
> Thank you.

[![NPM version](https://img.shields.io/npm/v/mdn-data.svg)](https://www.npmjs.com/package/mdn-data)
[![lint](https://github.com/mdn/data/actions/workflows/lint.yml/badge.svg)](https://github.com/mdn/data/actions/workflows/lint.yml)

This repository contains general data for Web technologies and is maintained by the [MDN team at Mozilla](https://wiki.mozilla.org/MDN).

## Repository contents

The data in this repository is used in MDN Web Docs to build [information boxes](https://developer.mozilla.org/en-US/docs/Web/CSS/background) and [sidebar navigation](https://developer.mozilla.org/en-US/docs/Web/API/Window).
External tools make use of this data as well, for example, the [CSSTree](https://github.com/csstree/csstree/) CSS parser.

There's a top-level directory for each broad area covered: for example, `api` and `css`.
Inside each of these directories is one or more JSON files containing the data.

### api

Contains data about Web APIs:

- API inheritance (interface inheritance and mixin implementations)

### css

Contains data about:

- CSS at-rules
- CSS functions
- CSS properties
- CSS selectors
- CSS syntaxes
- CSS types
- CSS units

For more information, see the [CSS data](./css/README.md) documentation and the [Updating CSS JSON DB](./docs/updating_css_json.md) guide.

### l10n

The l10n folder contains localization strings that are used in the various
json files throughout this repository.

## Problems?

If you find a problem, please [file an issue](https://github.com/mdn/data/issues/new).

## Contributing

We're very happy to accept contributions to this data.
Please familiarize yourself with the schema for the data you're editing, and send us a pull request.
See the [CONTRIBUTING.md](./CONTRIBUTING.md) document for more information.

## See also

- [https://github.com/mdn/browser-compat-data](https://github.com/mdn/browser-compat-data)
  for compatibility data for Web technologies
