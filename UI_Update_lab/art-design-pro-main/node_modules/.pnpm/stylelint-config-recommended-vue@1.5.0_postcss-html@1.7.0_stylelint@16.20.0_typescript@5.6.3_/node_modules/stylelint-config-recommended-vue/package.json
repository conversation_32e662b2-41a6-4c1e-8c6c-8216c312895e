{"name": "stylelint-config-recommended-vue", "version": "1.5.0", "description": "The recommended shareable Vue config for Stylelint.", "keywords": ["stylelint", "stylelint-config", "recommended", "vue"], "main": "lib/index.js", "files": ["lib", "scss"], "engines": {"node": "^12 || >=14"}, "scripts": {"test": "mocha \"tests/lib/**/*.js\" --reporter dot --timeout 60000", "lint": "eslint .", "eslint-fix": "eslint . --fix", "version": "npm run test && git add ."}, "dependencies": {"semver": "^7.3.5", "stylelint-config-html": ">=1.0.0", "stylelint-config-recommended": ">=6.0.0"}, "peerDependencies": {"stylelint": ">=14.0.0", "postcss-html": "^1.0.0"}, "devDependencies": {"@ota-meshi/eslint-plugin": "^0.13.0", "eslint": "^8.0.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-json-schema-validator": "^4.0.0", "eslint-plugin-jsonc": "^2.0.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-regexp": "^1.5.0", "eslint-plugin-vue": "^9.0.0", "eslint-plugin-yml": "^1.0.0", "mocha": "^10.0.0", "prettier": "^2.4.1", "stylelint": "^15.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/ota-meshi/stylelint-config-recommended-vue.git"}, "author": "<PERSON><PERSON> (https://github.com/ota-meshi)", "funding": "https://github.com/sponsors/ota-meshi", "license": "MIT", "bugs": {"url": "https://github.com/ota-meshi/stylelint-config-recommended-vue/issues"}, "homepage": "https://github.com/ota-meshi/stylelint-config-recommended-vue#readme"}