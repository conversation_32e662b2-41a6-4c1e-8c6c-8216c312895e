{"name": "stylelint-order", "version": "6.0.4", "description": "A collection of order related linting rules for Stylelint.", "keywords": ["stylelint-plugin", "stylelint", "css", "lint", "order"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": "hudochenkov/stylelint-order", "files": ["rules", "utils", "!**/tests", "index.js", "!.DS_Store"], "main": "index.js", "dependencies": {"postcss": "^8.4.32", "postcss-sorting": "^8.0.2"}, "peerDependencies": {"stylelint": "^14.0.0 || ^15.0.0 || ^16.0.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-hudochenkov": "^9.0.0", "eslint-config-prettier": "^9.1.0", "husky": "^8.0.3", "jest": "^29.7.0", "jest-light-runner": "^0.6.0", "jest-preset-stylelint": "^7.0.0", "jest-watch-typeahead": "^2.2.2", "lint-staged": "^15.2.0", "postcss-html": "^1.5.0", "postcss-less": "^6.0.0", "postcss-styled-syntax": "^0.5.0", "prettier": "~3.1.0", "prettier-config-hudochenkov": "^0.4.0", "stylelint": "^16.0.1"}, "scripts": {"lint": "eslint . --max-warnings=0 && prettier '**/*.js' --check", "test": "jest", "watch": "jest --watch", "coverage": "jest --coverage", "fix": "eslint . --fix --max-warnings=0 && prettier '**/*.js' --write", "prepare": "husky install"}, "lint-staged": {"*.js": ["eslint --fix --max-warnings=0", "prettier --write"]}, "jest": {"runner": "jest-light-runner", "preset": "jest-preset-stylelint", "setupFiles": ["./jest-setup.js"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "testEnvironment": "node", "testRegex": ".*\\.test\\.js$|rules/.*/tests/.*\\.js$"}, "prettier": "prettier-config-hudo<PERSON>v"}