{"name": "stylelint-config-recommended-scss", "version": "14.1.0", "description": "The recommended shareable SCSS config for Stylelint", "keywords": ["stylelint", "stylelint-config", "recommended", "scss"], "repository": "stylelint-scss/stylelint-config-recommended-scss", "license": "MIT", "author": "<PERSON><PERSON><PERSON><PERSON>", "main": "index.js", "files": ["index.js"], "engines": {"node": ">=18.12.0"}, "scripts": {"format": "prettier . --write --ignore-path=.prettierignore", "lint": "npm-run-all --parallel lint:*", "lint:formatting": "prettier . --check", "lint:js": "eslint . --ignore-path .gitignore", "lint:md": "remark . --quiet --frail --ignore-path .gitignore", "release": "np", "test": "node --test", "watch": "node --test --watch"}, "dependencies": {"postcss-scss": "^4.0.9", "stylelint-config-recommended": "^14.0.1", "stylelint-scss": "^6.4.0"}, "devDependencies": {"@stylelint/prettier-config": "^3.0.0", "@stylelint/remark-preset": "^5.1.1", "eslint": "^8.57.0", "eslint-config-stylelint": "^21.0.0", "np": "^10.0.6", "npm-run-all2": "^5.0.2", "prettier": "^3.3.2", "remark-cli": "^12.0.1", "stylelint": "^16.6.1"}, "peerDependencies": {"postcss": "^8.3.3", "stylelint": "^16.6.1"}, "peerDependenciesMeta": {"postcss": {"optional": true}}}